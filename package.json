{"name": "product-builder", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@faker-js/faker": "^9.6.0", "@headlessui/react": "^2.2.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tailwindcss/vite": "^4.1.3", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "tailwindcss": "^4.1.3", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}